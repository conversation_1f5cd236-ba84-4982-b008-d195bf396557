/**
 * Test suite for Electron fixes
 * Tests that the __dirname and CSP issues have been resolved
 */

import { describe, expect, it } from '@jest/globals';
import * as fs from 'fs';
import * as path from 'path';

describe('Electron Fixes', () => {
  describe('Configuration Files', () => {
    it('should have webpack renderer config file', () => {
      const configPath = path.join(
        __dirname,
        '../../webpack.renderer.config.js',
      );
      expect(fs.existsSync(configPath)).toBe(true);
    });

    it('should have forge config file', () => {
      const configPath = path.join(__dirname, '../../forge.config.js');
      expect(fs.existsSync(configPath)).toBe(true);
    });

    it('should have proper HTML template with CSP', () => {
      const htmlPath = path.join(__dirname, '../../src/index.html');
      expect(fs.existsSync(htmlPath)).toBe(true);

      const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
      expect(htmlContent).toContain('Content-Security-Policy');
    });
  });

  describe('Build Process', () => {
    it('should have proper package.json scripts', () => {
      const packageJson = require('../../package.json');

      expect(packageJson.scripts).toBeDefined();
      expect(packageJson.scripts.start).toBeDefined();
      expect(packageJson.scripts['build:all']).toBeDefined();
      expect(packageJson.scripts['build:renderer']).toBeDefined();
      expect(packageJson.scripts['build:preload']).toBeDefined();
    });
  });

  describe('Fixes Verification', () => {
    it('should have applied __dirname fixes', () => {
      // Test that the configuration changes have been applied
      const rendererConfigPath = path.join(
        __dirname,
        '../../webpack.renderer.config.js',
      );
      const configContent = fs.readFileSync(rendererConfigPath, 'utf-8');

      // Should have node configuration with mock
      expect(configContent).toContain("__dirname: 'mock'");
      expect(configContent).toContain("__filename: 'mock'");

      // Should have externals configuration
      expect(configContent).toContain('externals:');
      expect(configContent).toContain('better-sqlite3');
    });

    it('should have applied CSP fixes', () => {
      const htmlPath = path.join(__dirname, '../../src/index.html');
      const htmlContent = fs.readFileSync(htmlPath, 'utf-8');

      // Should have CSP meta tag
      expect(htmlContent).toContain('http-equiv="Content-Security-Policy"');
      expect(htmlContent).toContain("default-src 'self'");
      // Should allow unsafe-eval for webpack development mode
      expect(htmlContent).toContain("'unsafe-eval'");
    });
  });
});
