/**
 * Unit tests for Stagewise Toolbar Integration
 * Tests the core functionality of stagewise toolbar initialization
 */

describe('Stagewise Toolbar Module', () => {
  let mockInitToolbar: jest.Mock;
  let originalNodeEnv: string | undefined;

  beforeEach(() => {
    // Store original NODE_ENV
    originalNodeEnv = process.env.NODE_ENV;

    // Create mock function
    mockInitToolbar = jest.fn();

    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original NODE_ENV
    if (originalNodeEnv !== undefined) {
      process.env.NODE_ENV = originalNodeEnv;
    } else {
      delete process.env.NODE_ENV;
    }
  });

  describe('Package Availability', () => {
    it('should have @stagewise/toolbar-react package installed', () => {
      // Test that the package is available in node_modules
      const packageJson = require('../../package.json');
      expect(
        packageJson.dependencies['@stagewise/toolbar-react'],
      ).toBeDefined();
      expect(
        packageJson.dependencies['@stagewise-plugins/react'],
      ).toBeDefined();
    });

    it('should have correct package versions', () => {
      const packageJson = require('../../package.json');
      expect(packageJson.dependencies['@stagewise/toolbar-react']).toBe(
        '^0.6.2',
      );
      expect(packageJson.dependencies['@stagewise-plugins/react']).toBe(
        '^0.6.2',
      );
    });
  });

  describe('Environment Detection', () => {
    it('should detect development environment correctly', () => {
      process.env.NODE_ENV = 'development';
      expect(process.env.NODE_ENV).toBe('development');
    });

    it('should detect production environment correctly', () => {
      process.env.NODE_ENV = 'production';
      expect(process.env.NODE_ENV).toBe('production');
    });

    it('should handle undefined NODE_ENV', () => {
      delete process.env.NODE_ENV;
      expect(process.env.NODE_ENV).toBeUndefined();
    });
  });

  describe('Toolbar Configuration', () => {
    it('should create valid toolbar configuration', () => {
      const config = {
        plugins: [],
      };

      expect(config).toEqual({
        plugins: expect.any(Array),
      });
      expect(config.plugins).toHaveLength(0);
    });

    it('should support plugin configuration', () => {
      const config = {
        plugins: [
          {
            name: 'test-plugin',
            description: 'Test plugin',
          },
        ],
      };

      expect(config.plugins).toHaveLength(1);
      expect(config.plugins[0]).toHaveProperty('name', 'test-plugin');
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors', () => {
      const mockInit = jest.fn().mockImplementation(() => {
        throw new Error('Initialization failed');
      });

      expect(() => {
        mockInit({ plugins: [] });
      }).toThrow('Initialization failed');
    });

    it('should handle invalid function types', () => {
      const invalidInit = 'not-a-function';
      expect(typeof invalidInit).not.toBe('function');
    });
  });

  describe('Logging Functionality', () => {
    let consoleLogSpy: jest.SpyInstance;
    let consoleErrorSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
      consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    });

    afterEach(() => {
      consoleLogSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should log initialization steps', () => {
      console.log('🔍 STAGEWISE: Starting initialization...');
      console.log('🔍 STAGEWISE: Environment check passed');
      console.log('🔍 STAGEWISE: Toolbar initialized successfully');

      expect(consoleLogSpy).toHaveBeenCalledWith(
        '🔍 STAGEWISE: Starting initialization...',
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        '🔍 STAGEWISE: Environment check passed',
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        '🔍 STAGEWISE: Toolbar initialized successfully',
      );
    });

    it('should log errors with details', () => {
      const error = new Error('Test error');
      console.error('🔍 STAGEWISE: Failed to initialize:', error);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '🔍 STAGEWISE: Failed to initialize:',
        error,
      );
    });
  });

  describe('Status Management', () => {
    it('should track initialization status', () => {
      const statusStates = [
        'not-initialized',
        'initializing',
        'initialized',
        'init-error',
        'import-error',
        'skipped-production',
        'invalid-export',
      ];

      statusStates.forEach(status => {
        expect(typeof status).toBe('string');
        expect(status.length).toBeGreaterThan(0);
      });
    });

    it('should validate status transitions', () => {
      let status = 'not-initialized';

      // Valid transition
      status = 'initializing';
      expect(status).toBe('initializing');

      // Valid success transition
      status = 'initialized';
      expect(status).toBe('initialized');

      // Valid error transition
      status = 'init-error';
      expect(status).toBe('init-error');
    });
  });
});
