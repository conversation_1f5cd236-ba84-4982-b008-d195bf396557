import { getDatabaseConfig } from '../../config/database-config';
import { PlaylistIPCHandlers } from '../../ipc/playlist-ipc-handlers';
import { DatabaseService } from '../../services/database-service';
import { StructuredLoggerService } from '../../services/structured-logger-service';

let playlistIPCHandlers: PlaylistIPCHandlers | null = null;

export async function registerPlaylistHandlers(): Promise<void> {
  try {
    // Initialize database service
    const databaseConfig = getDatabaseConfig();
    const databaseService = new DatabaseService(databaseConfig);
    await databaseService.initialize();

    // Initialize logger
    const logger = new StructuredLoggerService();

    // Create and register the real IPC handlers
    playlistIPCHandlers = new PlaylistIPCHandlers(databaseService, logger);
    playlistIPCHandlers.registerHandlers();

    console.log('✅ Real playlist IPC handlers registered successfully');
  } catch (error) {
    console.error('❌ Failed to register playlist IPC handlers:', error);
    throw error;
  }
}

export function cleanupPlaylistHandlers(): void {
  if (playlistIPCHandlers) {
    playlistIPCHandlers.unregisterHandlers();
    playlistIPCHandlers = null;
    console.log('✅ Playlist IPC handlers cleaned up');
  }
}
