const path = require('path');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  target: 'electron-renderer',
  entry: './src/renderer-router.tsx',
  
  // Configure webpack dev server with reliable settings
  devServer: {
    port: 3002,
    hot: true,
    liveReload: true,
    historyApiFallback: true,
    compress: true,
    static: false,
    client: {
      logging: 'warn',
      overlay: {
        errors: true,
        warnings: false,
      },
    },
    // Disable host checking for electron
    allowedHosts: 'all',
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  node: {
    __dirname: 'mock',
    __filename: 'mock',
  },
  mode: isProduction ? 'production' : 'development',
  devtool: isProduction ? 'source-map' : 'eval-cheap-module-source-map',

  output: {
    filename: 'index.js',
    path: path.resolve(__dirname, '.webpack/renderer/main_window'),
    publicPath: './',
    clean: true,
  },

  module: {
    rules: [
      // TypeScript and TSX files (primary loader with enhanced options)
      {
        test: /\.tsx?$/,
        exclude: /(node_modules|\.webpack)/,
        use: {
          loader: 'ts-loader',
          options: {
            transpileOnly: true,
            compilerOptions: {
              noEmit: false,
              sourceMap: process.env.NODE_ENV !== 'production',
              declaration: false, // Disable for webpack builds
              declarationMap: false,
            },
            configFile: 'tsconfig.json',
            // Enable faster builds with thread-loader in development
            happyPackMode: process.env.NODE_ENV !== 'production',
          },
        },
      },
      // JavaScript and JSX files (fallback for any JS files)
      {
        test: /\.jsx?$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  electron: '37.2.0',
                },
                useBuiltIns: 'usage',
                corejs: 3,
                modules: false, // Let webpack handle modules
              }],
              ['@babel/preset-react', {
                runtime: 'automatic',
                development: process.env.NODE_ENV !== 'production',
              }],
              '@babel/preset-typescript',
            ],
            plugins: [
              '@babel/plugin-proposal-class-properties',
              '@babel/plugin-proposal-object-rest-spread',
              '@babel/plugin-syntax-dynamic-import',
            ],
            cacheDirectory: true,
            cacheCompression: false,
          },
        },
      },
      // Images and assets with optimized handling
      {
        test: /\.(png|jpe?g|gif|svg|ico)$/i,
        type: 'asset',
        generator: {
          filename: 'assets/images/[name].[contenthash:8][ext]',
        },
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024, // 8kb - inline smaller images
          },
        },
      },
      // Fonts with optimized handling
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/fonts/[name].[contenthash:8][ext]',
        },
      },
      // Audio and video files
      {
        test: /\.(mp3|mp4|wav|ogg|webm)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'assets/media/[name].[contenthash:8][ext]',
        },
      },
      // JSON files
      {
        test: /\.json$/,
        type: 'json',
      },
      // Raw text files and documentation
      {
        test: /\.(txt|md)$/i,
        type: 'asset/source',
      },
      // Enhanced CSS processing with PostCSS and TailwindCSS
      {
        test: /\.css$/,
        use: [
          { 
            loader: 'style-loader',
            options: {
              // Insert styles at the top of <head> for better performance
              insert: 'head',
              injectType: 'singletonStyleTag',
            },
          },
          { 
            loader: 'css-loader',
            options: {
              importLoaders: 1,
              sourceMap: !isProduction,
              modules: {
                auto: true, // Enable CSS modules for files with .module.css
                localIdentName: isProduction 
                  ? '[hash:base64:8]' 
                  : '[name]__[local]--[hash:base64:5]',
              },
            },
          },
          { 
            loader: 'postcss-loader',
            options: {
              sourceMap: !isProduction,
              postcssOptions: {
                config: path.resolve(__dirname, 'postcss.config.js'),
              },
            },
          },
        ],
      },
      // SCSS/SASS support (if needed in the future)
      {
        test: /\.s[ac]ss$/i,
        use: [
          'style-loader',
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
              sourceMap: !isProduction,
            },
          },
          'postcss-loader',
          'sass-loader',
        ],
      },
    ],
  },
  
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css', '.scss', '.sass', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@/components': path.resolve(__dirname, 'src/components'),
      '@/services': path.resolve(__dirname, 'src/services'),
      '@/utils': path.resolve(__dirname, 'src/utils'),
      '@/types': path.resolve(__dirname, 'src/types'),
      '@/shared': path.resolve(__dirname, 'src/shared'),
      '@/handlers': path.resolve(__dirname, 'src/handlers'),
      '@/repositories': path.resolve(__dirname, 'src/repositories'),
      '@/adapters': path.resolve(__dirname, 'src/adapters'),
      '@/styles': path.resolve(__dirname, 'src/styles'),
      '@/lib': path.resolve(__dirname, 'src/lib'),
      '@/frontend': path.resolve(__dirname, 'src/frontend'),
      '@/backend': path.resolve(__dirname, 'src/backend'),
      // Fix for tiny-warning module resolution issue
      'tiny-warning': path.resolve(__dirname, 'node_modules/tiny-warning/dist/tiny-warning.cjs.js'),
      // Fix for stagewise modules
      '@stagewise/toolbar': path.resolve(__dirname, 'node_modules/@stagewise/toolbar/dist/index.es.js'),
      '@stagewise/toolbar-react': path.resolve(__dirname, 'node_modules/@stagewise/toolbar-react/dist/index.js'),
      '@stagewise-plugins/react': path.resolve(__dirname, 'node_modules/@stagewise-plugins/react/dist/index.es.js'),
    },
    fallback: {
      'fs': false,
      'path': false,
      'os': false,
    },
  },

  // Externalize native modules and Node.js modules that should not be bundled
  externals: {
    'better-sqlite3': 'commonjs better-sqlite3',
    'sqlite3': 'commonjs sqlite3',
    'electron': 'commonjs electron',
    'fs': 'commonjs fs',
    'path': 'commonjs path',
    'os': 'commonjs os',
    'yt-dlp-wrap': 'commonjs yt-dlp-wrap',
    'fluent-ffmpeg': 'commonjs fluent-ffmpeg',
    'winston': 'commonjs winston',
  },

  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html',
      filename: 'index.html',
      inject: true,
    }),

    new ForkTsCheckerWebpackPlugin({
      typescript: {
        configFile: path.resolve(__dirname, 'tsconfig.json'),
        diagnosticOptions: {
          semantic: true,
          syntactic: true,
        },
        mode: 'write-references',
      },
      logger: 'webpack-infrastructure',
      async: !isProduction, // Async in development for faster builds
    }),

    // Define global variables to prevent ReferenceError
    new (require('webpack')).DefinePlugin({
      'global': 'globalThis',
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process.platform': JSON.stringify(process.platform),
      'process.env': JSON.stringify(process.env),
      // Define __dirname as empty string to prevent asset relocator loader issues
      '__dirname': JSON.stringify('/'),
    }),

    // Custom plugin to remove asset relocator loader runtime
    {
      apply: (compiler) => {
        compiler.hooks.compilation.tap('RemoveAssetRelocatorPlugin', (compilation) => {
          compilation.hooks.processAssets.tap(
            {
              name: 'RemoveAssetRelocatorPlugin',
              stage: compilation.PROCESS_ASSETS_STAGE_OPTIMIZE,
            },
            (assets) => {
              Object.keys(assets).forEach((assetName) => {
                if (assetName.endsWith('.js')) {
                  const asset = assets[assetName];
                  let source = asset.source();
                  if (typeof source === 'string') {
                    // Remove the asset relocator loader runtime that uses __dirname
                    source = source.replace(
                      /\/\*\*\*\*\*\*\/ \t\/\* webpack\/runtime\/asset-relocator-loader \*\/[\s\S]*?if \(typeof __webpack_require__ !== 'undefined'\) __webpack_require__\.ab = __dirname \+ "\/native_modules\/";/g,
                      '/* asset-relocator-loader runtime removed */'
                    );
                    compilation.updateAsset(assetName, new (require('webpack').sources.RawSource)(source));
                  }
                }
              });
            }
          );
        });
      },
    },
  ],
  
  optimization: {
    // Minimal optimization for faster dev builds
    minimize: false,
    splitChunks: isProduction ? {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    } : false,
  },

  // Disable problematic webpack features for renderer
  experiments: {
    topLevelAwait: false,
  },
  
  performance: {
    hints: isProduction ? 'warning' : false,
    maxEntrypointSize: 1024000, // 1MB
    maxAssetSize: 512000, // 512KB
  },
  
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    timings: true,
    builtAt: true,
  },
  

  
  // Simple cache configuration for development
  cache: isProduction ? {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
  } : false,
};