import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { App } from '../../src/App';

// Mock the stagewise toolbar module
const mockInitToolbar = jest.fn();
const mockStagewiseModule = {
  initToolbar: mockInitToolbar,
};

// Mock the dynamic import
jest.mock('@stagewise/toolbar', () => mockStagewiseModule);

// Mock the router
jest.mock('../../src/frontend/lib/router', () => ({
  router: {
    state: { status: 'idle' },
  },
}));

// Mock the query client
jest.mock('../../src/frontend/lib/query-client', () => ({
  queryClient: new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  }),
}));

// Mock CSS imports
jest.mock('../../src/styles/globals.css', () => ({}));

describe('Stagewise Integration', () => {
  let originalEnv: string | undefined;
  let consoleLogSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    // Store original NODE_ENV
    originalEnv = process.env.NODE_ENV;
    
    // Setup console spies
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original NODE_ENV
    if (originalEnv !== undefined) {
      process.env.NODE_ENV = originalEnv;
    } else {
      delete process.env.NODE_ENV;
    }
    
    // Restore console methods
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  describe('Development Environment', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development';
    });

    it('should initialize stagewise toolbar in development mode', async () => {
      render(<App />);

      // Wait for the component to mount and stagewise to initialize
      await waitFor(() => {
        expect(consoleLogSpy).toHaveBeenCalledWith('🔍 STAGEWISE: Starting initialization...');
      });

      // Check that the toolbar was initialized
      await waitFor(() => {
        expect(mockInitToolbar).toHaveBeenCalledWith({
          plugins: [],
        });
      });

      // Check success logs
      expect(consoleLogSpy).toHaveBeenCalledWith('🔍 STAGEWISE: Toolbar initialized successfully');
    });

    it('should display stagewise status in debug info', async () => {
      render(<App />);

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByText(/Stagewise: initialized/)).toBeInTheDocument();
      });
    });

    it('should handle stagewise import errors gracefully', async () => {
      // Mock import failure
      const importError = new Error('Module not found');
      jest.doMock('@stagewise/toolbar', () => {
        throw importError;
      });

      render(<App />);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          '🔍 STAGEWISE: Failed to import toolbar module:',
          expect.any(Error)
        );
      });
    });

    it('should handle stagewise initialization errors', async () => {
      // Mock initialization failure
      mockInitToolbar.mockImplementation(() => {
        throw new Error('Initialization failed');
      });

      render(<App />);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          '🔍 STAGEWISE: Error during initialization:',
          expect.any(Error)
        );
      });
    });

    it('should log comprehensive debugging information', async () => {
      render(<App />);

      // Check that all debug logs are present
      expect(consoleLogSpy).toHaveBeenCalledWith('🔍 DEBUGGING: App component rendering...');
      expect(consoleLogSpy).toHaveBeenCalledWith('🔍 DEBUGGING: NODE_ENV:', 'development');
      expect(consoleLogSpy).toHaveBeenCalledWith('🔍 DEBUGGING: typeof window:', 'object');
      expect(consoleLogSpy).toHaveBeenCalledWith('🔍 DEBUGGING: typeof document:', 'object');
    });
  });

  describe('Production Environment', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    it('should skip stagewise initialization in production mode', async () => {
      render(<App />);

      await waitFor(() => {
        expect(consoleLogSpy).toHaveBeenCalledWith('🔍 STAGEWISE: Skipped - not in development mode');
      });

      // Should not attempt to initialize
      expect(mockInitToolbar).not.toHaveBeenCalled();
    });

    it('should display correct status in production', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/Stagewise: skipped-production/)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development';
    });

    it('should handle missing initToolbar export', async () => {
      // Mock module without initToolbar
      jest.doMock('@stagewise/toolbar', () => ({
        someOtherExport: jest.fn(),
      }));

      render(<App />);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          '🔍 STAGEWISE: initToolbar is not a function:',
          'undefined'
        );
      });
    });

    it('should display error status when initialization fails', async () => {
      mockInitToolbar.mockImplementation(() => {
        throw new Error('Init failed');
      });

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/Stagewise: init-error/)).toBeInTheDocument();
      });
    });
  });

  describe('Debug Information Display', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development';
    });

    it('should display NODE_ENV in debug info', async () => {
      render(<App />);

      expect(screen.getByText(/NODE_ENV: development/)).toBeInTheDocument();
    });

    it('should display blue debug box in development', async () => {
      render(<App />);

      expect(screen.getByText(/Stagewise should be here/)).toBeInTheDocument();
    });
  });
});
