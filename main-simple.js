const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Initialize settings handlers
const initializeSettingsHandlers = () => {
  console.log('🔧 Initializing settings handlers...');

  // Mock settings data
  const defaultSettings = {
    theme: 'light',
    language: 'en',
    downloadLocation: '',
    tempDirectory: '',
    startMinimized: false,
    closeToTray: false,
    autoUpdate: true,
    videoQuality: 'best',
    maxConcurrentDownloads: 3,
    windowSize: { width: 1200, height: 800 },
    windowPosition: { x: 100, y: 100 },
    notificationsEnabled: true,
  };

  // Register settings:getAll handler
  ipcMain.handle('settings:getAll', async () => {
    console.log('📋 Getting all settings');
    return defaultSettings;
  });

  // Register settings:set handler
  ipcMain.handle('settings:set', async (event, key, value) => {
    console.log(`⚙️ Setting ${key} to:`, value);
    return { success: true };
  });

  // Register settings:get handler
  ipcMain.handle('settings:get', async (event, key) => {
    console.log(`📖 Getting setting: ${key}`);
    return defaultSettings[key];
  });

  // Register settings:reset handler
  ipcMain.handle('settings:reset', async () => {
    console.log('🔄 Resetting all settings');
    return { success: true };
  });

  console.log('✅ Settings handlers registered successfully');
};

// Handle creating/removing shortcuts on Windows when installing/uninstalling
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Development features
const isDevelopment = process.env.NODE_ENV === 'development';
let mainWindow;

const createWindow = () => {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    minWidth: 600,
    minHeight: 400,
    center: true,
    title: 'Playlistify',
    show: true,
    webPreferences: {
      preload: path.join(__dirname, '.webpack/renderer/main_window/preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // Disable web security for Stagewise to work
      allowRunningInsecureContent: true,
    },
  });

  // Load the built HTML file
  const htmlPath = path.join(
    __dirname,
    '.webpack/renderer/main_window/index.html',
  );
  console.log('Loading HTML from:', htmlPath);
  mainWindow.loadFile(htmlPath);

  // Open DevTools in development
  if (isDevelopment) {
    mainWindow.webContents.openDevTools();
  }

  // Add reload functionality for development
  if (isDevelopment) {
    // Reload on F5
    mainWindow.webContents.on('before-input-event', (event, input) => {
      if (input.key === 'F5') {
        console.log('Reloading application...');
        mainWindow.reload();
      }
    });

    // Watch for file changes and reload (simple file watcher)
    const watchPath = path.join(__dirname, '.webpack');
    if (fs.existsSync(watchPath)) {
      fs.watch(watchPath, { recursive: true }, (eventType, filename) => {
        if (
          filename &&
          (filename.endsWith('.js') || filename.endsWith('.html'))
        ) {
          console.log(`File changed: ${filename}, reloading...`);
          setTimeout(() => {
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.reload();
            }
          }, 100);
        }
      });
    }
  }
};

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  initializeSettingsHandlers();
  createWindow();
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
