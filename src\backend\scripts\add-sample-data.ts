// src/backend/scripts/add-sample-data.ts

import { DatabaseService } from '../services/database-service';
import { getDatabaseConfig } from '../config/database-config';

async function addSampleData() {
  console.log('🌱 Adding sample data to database...');

  try {
    // Initialize database service
    const databaseConfig = getDatabaseConfig();
    const databaseService = new DatabaseService(databaseConfig);
    await databaseService.initialize();

    const playlistRepository = databaseService.getPlaylistRepository();
    const songRepository = databaseService.getSongRepository();

    // Create sample songs
    console.log('📀 Creating sample songs...');
    
    const song1 = await songRepository.create({
      title: 'Bohemian Rhapsody',
      artist: 'Queen',
      album: 'A Night at the Opera',
      duration: 355, // 5:55 in seconds
    });

    const song2 = await songRepository.create({
      title: 'Stairway to Heaven',
      artist: 'Led Zeppelin',
      album: 'Led Zeppelin IV',
      duration: 482, // 8:02 in seconds
    });

    const song3 = await songRepository.create({
      title: 'Hotel California',
      artist: 'Eagles',
      album: 'Hotel California',
      duration: 391, // 6:31 in seconds
    });

    const song4 = await songRepository.create({
      title: 'Sweet Child O\' Mine',
      artist: 'Guns N\' Roses',
      album: 'Appetite for Destruction',
      duration: 356, // 5:56 in seconds
    });

    const song5 = await songRepository.create({
      title: 'Imagine',
      artist: 'John Lennon',
      album: 'Imagine',
      duration: 183, // 3:03 in seconds
    });

    console.log(`✅ Created ${5} sample songs`);

    // Create sample playlists
    console.log('🎵 Creating sample playlists...');

    const playlist1 = await playlistRepository.create({
      name: 'Classic Rock Hits',
      description: 'The greatest classic rock songs of all time',
    });

    const playlist2 = await playlistRepository.create({
      name: 'Chill Vibes',
      description: 'Relaxing songs for a peaceful evening',
    });

    const playlist3 = await playlistRepository.create({
      name: 'Road Trip Anthems',
      description: 'Perfect songs for your next adventure',
    });

    console.log(`✅ Created ${3} sample playlists`);

    console.log('🎉 Sample data added successfully!');
    console.log('\nSample data summary:');
    console.log(`- Songs: ${5}`);
    console.log(`- Playlists: ${3}`);
    console.log('\nPlaylists created:');
    console.log(`- ${playlist1.name} (ID: ${playlist1.id})`);
    console.log(`- ${playlist2.name} (ID: ${playlist2.id})`);
    console.log(`- ${playlist3.name} (ID: ${playlist3.id})`);

    // Close database connection
    await databaseService.close();

  } catch (error) {
    console.error('❌ Failed to add sample data:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  addSampleData().then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

export { addSampleData };
